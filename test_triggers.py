#!/usr/bin/env python3
"""
Test script to verify trigger phrase detection is working
"""

# Import the classes from live.py
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from live import TriggerPhraseDetector, QuestionModeManager

def test_trigger_detection():
    """Test trigger phrase detection with various inputs"""
    
    detector = TriggerPhraseDetector()
    
    test_cases = [
        # Should trigger
        ("interview question what is react", True),
        ("can you explain javascript", True),
        ("what is python", True),
        ("how does angular work", True),
        ("tell me about databases", True),
        ("in a project where performance is slow", True),
        ("imagine you are building an app", True),
        ("suppose you have a component", True),
        
        # Should NOT trigger (casual conversation)
        ("hello how are you", False),
        ("the weather is nice today", False),
        ("I had lunch", False),
        ("see you later", False),
        ("good morning", False),
        
        # Edge cases
        ("what time is it", False),  # Not technical
        ("what is your name", False),  # Not technical
        ("what is react component", True),  # Technical
        ("how are you doing with react", True),  # Contains technical term
    ]
    
    print("🧪 Testing Trigger Phrase Detection")
    print("=" * 50)
    
    correct_predictions = 0
    total_tests = len(test_cases)
    
    for text, expected in test_cases:
        is_trigger, confidence, phrases = detector.detect_trigger(text)
        
        # Consider it correct if prediction matches expectation
        is_correct = (is_trigger and expected) or (not is_trigger and not expected)
        
        if is_correct:
            correct_predictions += 1
            status = "✅"
        else:
            status = "❌"
        
        print(f"{status} '{text}'")
        print(f"   Expected: {expected}, Got: {is_trigger} (confidence: {confidence:.2f})")
        if phrases:
            print(f"   Detected phrases: {phrases}")
        print()
    
    accuracy = (correct_predictions / total_tests) * 100
    print(f"📊 Accuracy: {correct_predictions}/{total_tests} ({accuracy:.1f}%)")
    
    return accuracy > 80  # Consider it successful if > 80% accuracy

def test_question_mode():
    """Test question mode state management"""
    
    print("\n🧪 Testing Question Mode Management")
    print("=" * 50)
    
    manager = QuestionModeManager()
    
    # Test initial state
    assert manager.get_mode() == "IDLE", "Should start in IDLE mode"
    print("✅ Initial state: IDLE")
    
    # Test manual activation
    manager.activate_manually()
    assert manager.get_mode() == "TRIGGERED", "Should be TRIGGERED after manual activation"
    assert manager.manual_activation == True, "Manual activation flag should be set"
    print("✅ Manual activation works")
    
    # Test mode transitions
    manager.set_mode("PROCESSING")
    assert manager.get_mode() == "PROCESSING", "Should transition to PROCESSING"
    print("✅ Mode transitions work")
    
    # Test should_process_audio
    assert manager.should_process_audio() == True, "Should process audio in PROCESSING mode"
    print("✅ Audio processing logic works")
    
    # Reset to IDLE
    manager.set_mode("IDLE")
    assert manager.should_process_audio() == False, "Should not process audio in IDLE mode"
    print("✅ IDLE mode blocks audio processing")
    
    return True

if __name__ == "__main__":
    print("🚀 Testing Enhanced Interview AI Components")
    print("=" * 60)
    
    try:
        # Test trigger detection
        trigger_success = test_trigger_detection()
        
        # Test question mode
        mode_success = test_question_mode()
        
        print("\n📋 Test Summary")
        print("=" * 30)
        print(f"Trigger Detection: {'✅ PASS' if trigger_success else '❌ FAIL'}")
        print(f"Question Mode: {'✅ PASS' if mode_success else '❌ FAIL'}")
        
        if trigger_success and mode_success:
            print("\n🎉 All tests passed! The enhanced system should work correctly.")
            print("\n💡 Try these phrases in the app:")
            print("   - 'interview question what is react'")
            print("   - 'can you explain javascript'")
            print("   - 'what is python'")
            print("   - Press Ctrl+Space then ask any question")
        else:
            print("\n⚠️ Some tests failed. Check the implementation.")
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
